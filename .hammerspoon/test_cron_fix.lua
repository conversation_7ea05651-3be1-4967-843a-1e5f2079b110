-- 测试 CronTask 修复的验证脚本
-- 在 Hammerspoon 控制台中运行: dofile(hs.configdir .. "/test_cron_fix.lua")

local spoonPath = hs.configdir .. "/Spoons/TaskList.spoon"
local cronTask = dofile(spoonPath .. "/cron_task.lua")

print("=== CronTask 修复验证 ===")

-- 测试数据
local testCronTasks = {
    {
        type = "@daily",
        task = "每天刮胡子",
    },
    {
        type = "@weekly", 
        task = "【每周清洁】",
    }
}

-- 测试 ID 幂等性
print("测试 daily 任务 ID 幂等性:")
local task1_first = cronTask.convertToTaskListFormat({testCronTasks[1]})[1]
hs.timer.usleep(1000) -- 等待1毫秒
local task1_second = cronTask.convertToTaskListFormat({testCronTasks[1]})[1]

print("第一次 ID:", task1_first.id, "addTime:", task1_first.addTime, "date:", task1_first.date)
print("第二次 ID:", task1_second.id, "addTime:", task1_second.addTime, "date:", task1_second.date)
print("ID 相同:", task1_first.id == task1_second.id)

print("\n测试 weekly 任务:")
local task2 = cronTask.convertToTaskListFormat({testCronTasks[2]})[1]
print("Weekly 任务 date:", task2.date, "ID:", task2.id)

print("\n=== 验证完成 ===")
